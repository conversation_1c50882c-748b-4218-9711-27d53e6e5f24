# APP单页引导功能设计文档

## 1. 功能概述

简洁的后端系统，支持APP在特定页面上线新功能时向用户展示一次性引导提示。

## 2. 数据库设计

### 2.1 引导表(page_guide)
```sql
CREATE TABLE `page_guide` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `page_key` varchar(50) NOT NULL COMMENT '页面标识',
  `title` varchar(100) NOT NULL COMMENT '引导标题',
  `content` text COMMENT '引导内容',
  `image_url` varchar(255) DEFAULT NULL COMMENT '图片URL',
  `target_element` varchar(100) DEFAULT NULL COMMENT '目标元素ID',
  `position` varchar(20) DEFAULT 'center' COMMENT '位置',
  `app_version` varchar(20) DEFAULT NULL COMMENT 'APP版本',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_page_key` (`page_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 2.2 用户已读记录表(user_guide_read)
```sql
CREATE TABLE `user_guide_read` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `guide_id` bigint(20) NOT NULL COMMENT '引导ID',
  `read_time` datetime NOT NULL COMMENT '阅读时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_guide` (`user_id`,`guide_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 3. 接口设计

### 3.1 获取页面引导

#### 请求
```
GET /api/guides
```

#### 参数
| 参数名 | 类型 | 必填 | 描述 |
| --- | --- | --- | --- |
| pageKey | String | 是 | 页面标识 |
| userId | Long | 是 | 用户ID |
| appVersion | String | 是 | APP版本号 |

#### 响应
```json
{
  "code": 200,
  "data": {
    "guideId": 1,
    "title": "筛选功能",
    "content": "点击此处使用新筛选功能",
    "imageUrl": "https://example.com/filter.png",
    "targetElement": "filter_btn",
    "position": "top-right"
  }
}
```

### 3.2 标记已读

#### 请求
```
POST /api/guides/read
```

#### 参数
```json
{
  "userId": 1001,
  "guideId": 1
}
```

#### 响应
```json
{
  "code": 200,
  "data": true
}
```

## 4. 核心逻辑

### 4.1 引导展示逻辑
1. 用户进入页面，客户端请求后端获取引导
2. 后端根据页面标识、版本匹配、用户未阅读过条件筛选
3. 返回匹配的引导给客户端
4. 客户端展示引导提示

### 4.2 标记已读逻辑
1. 用户查看引导后，客户端调用标记已读接口
2. 后端记录用户已读状态
3. 已读的引导不再向该用户展示

## 5. 实现示例

```java
@Service
public class PageGuideServiceImpl implements PageGuideService {

    @Resource
    private PageGuideMapper guideMapper;
    
    @Resource
    private UserGuideReadMapper readMapper;
    
    @Override
    public GuideVO getGuide(String pageKey, Long userId, String appVersion) {
        // 获取页面引导
        PageGuide guide = guideMapper.selectValidGuide(pageKey, appVersion);
        if (guide == null) return null;
        
        // 检查用户是否已读
        boolean hasRead = readMapper.checkUserRead(userId, guide.getId());
        if (hasRead) return null;
        
        // 转换并返回
        return convertToVO(guide);
    }
    
    @Override
    public boolean markRead(Long userId, Long guideId) {
        UserGuideRead record = new UserGuideRead();
        record.setUserId(userId);
        record.setGuideId(guideId);
        record.setReadTime(new Date());
        return readMapper.insert(record) > 0;
    }
}
```

## 6. 配置与缓存

```properties
# 引导功能配置
guide.cache.enabled=true
guide.cache.expire=300
```

- 使用本地缓存存储活跃页面的引导信息
- 缓存时间5分钟
- 用户已读记录实时更新